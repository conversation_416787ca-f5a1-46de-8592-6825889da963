{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Voyageai Embedding Function Schema", "description": "Schema for the voyageai embedding function configuration", "version": "1.0.0", "type": "object", "properties": {"model_name": {"type": "string", "description": "Parameter model_name for the voyageai embedding function"}, "api_key_env_var": {"type": "string", "description": "Parameter api_key_env_var for the voyageai embedding function"}, "input_type": {"type": "string", "description": "Parameter input_type for the voyageai embedding function"}, "truncation": {"type": "boolean", "description": "Parameter truncation for the voyageai embedding function"}}, "required": ["api_key_env_var", "model_name"], "additionalProperties": false}
import math;

def armstrong(n):
    result = 0
    power=len(str(n))
    a_n = n
    while a_n != 0:
        result += (a_n%10) ** power
        a_n //= 10
    return result == n 

def factors(num):
    fac_list = [1,num]
    fac=2
    while fac <= int(math.sqrt(num)):
        if(num % fac == 0 ):
            fac_list.append(fac)
            if(num//fac != fac):
                fac_list.append(num//fac)
        fac+=1
    fac_list.sort()
    return fac_list;        


def frequencies(num_arr):
    new_dict = dict.fromkeys(num_arr,0)
    for i in range(len(num_arr)):
        # new_dict[num_arr[i]] = new_dict.get(num_arr[i],0) + 1
        new_dict[num_arr[i]] +=1
    return new_dict            

def existanceArr(arr1,arr2) :
    hash_arr1 = frequencies(arr1)
    print(hash_arr1)
    for i in arr2:
        print(i)
        print("existance in arr1 : ",hash_arr1.get(i,0))
    

def pallim(n):
    num = n
    rev_num = 0
    while num!=0:
        rev_num *= 10 
        rev_num+= num%10
        num //= 10
    return n==rev_num    

# num_array = input("enter comma sparated values : ").split(",")
# num = int(input("Enter a number: "))
# print(pallim(num))
# print(armstrong(num))
# print(factors(num))

# arr1 = input("enter comma sparated values for arr1 : ").split(",")
# arr2 = input("enter comma sparated values for arr2 : ").split(",")


# print(existanceArr(arr1,arr2))

def factorial_recur(num):
    if(num == 1):
        return 1
    return num * factorial_recur(num-1)


# def recur_print(times):
#     if(times==1):
#         return times
#     return times * recur_print(times-1)

def rev_array(arr,left=0,right=0):
    if(right == 0):
        right = len(arr)-1
    if(left == right or left > right):
        return arr
    arr[left],arr[right] = arr[right],arr[left]
    return rev_array(arr,left+1,right-1)

def rev_arr_recur(arr,rev_arr = []):
    if(arr == []):
        return rev_arr
    rev_arr.append(arr.pop())
    return rev_arr_recur(arr,rev_arr)

# r_p(5)->r_p(4)->r_p(3)->r_p(2)->r_p(1)->r_p(0)
# 

def check_pallim(str):
    l = 0
    r = len(str)-1
    if len(str) <= 1:
        return True
    print("l : ",l," r : ",r)
    if(str[l] != str[r]):
        return False
    return check_pallim(str[l+1:r])

arr = [1,2,3,4,5]
# number = int(input("Enter the number : "))
# arr = list(map(int,input("enter a comma separaed number array : ").split(',')))
print(check_pallim("rohor"))